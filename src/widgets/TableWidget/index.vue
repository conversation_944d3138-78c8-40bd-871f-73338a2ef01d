<template>
  <div class="jimu-widget jimu-tool-widget table-widget" v-show="show">
    <!-- 标签以及表格 -->
    <a-icon type="close" class="btn-close" @click="closeHandler" />
    <a-tabs v-model="activeKey" hide-add type="editable-card" @edit="editHandler">
      <a-tab-pane v-for="table in tables" :key="table.key" :tab="table.title" :closable="true">
        <component
          :is="table.component"
          :config="table"
          :geometryFilter="geometryFilter"
          :regionFilterData="regionFilterData"
          @rowSelect="rowSelectHandler"
          @rowUnselect="rowUnselectHandler"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import BaseWidget from "@/BaseWidget";

const DEFAULT_TABLE_COMPONENT = "widgets/TableWidget/components/DefaultTable";

export default {
  name: "TableWidget",

  mixins: [BaseWidget],

  data() {
    return {
      show: false,
      activeKey: null,
      tables: [],
      configMap: {},
      geometryFilter: null,
      regionFilterData: null,
    };
  },

  mounted() {
    this.bus.$on(this.$events.table.OPEN_TABLE, this.showTableHandler);
    this.bus.$on(this.$events.table.CLOSE_TABLE, this.hideTableHandler);
    this.bus.$on(this.$events.admin.CHANGE_ADMIN, this.adminFilterUpdateHandler);
    this.bus.$on(this.$events.map.GEOMETRY_FILTER_UPDATE, this.geometryFilterUpdateHandler);
  },

  methods: {
    onSearch(e) {
      console.log(e);
    },
    editHandler(targetKey, action) {
      if (action === "remove") {
        this.removeTable(targetKey);
        this.bus.$emit(this.$events.table.TABLE_CLOSED, targetKey);
      }
    },

    // 打开表格
    async showTableHandler(config) {
      if (!config || !config.key) return;
      const tableConfig = this.configMap[config.key];
      if (tableConfig) return;
      this.configMap[config.key] = config;
      // 加载表格组件
      const component = config.tableComponent ? config.tableComponent : DEFAULT_TABLE_COMPONENT;
      config.component = await this._loadModule(component);
      //
      this.tables = Object.values(this.configMap);
      this.activeKey = config.key;
      this.show = true;
    },

    hideTableHandler(config) {
      this.removeTable(config.key);
    },

    // 关闭表格
    removeTable(key) {
      if (!key) return;
      const tableConfig = this.configMap[key];
      if (!tableConfig) return;
      delete this.configMap[key];
      this.tables = Object.values(this.configMap);
      if (this.tables.length == 0) {
        this.show = false;
        return;
      }
      this.activeKey = this.tables[this.tables.length - 1].key;
    },

    adminFilterUpdateHandler() {
      // 获取区域过滤
      const regionFilterData = this.getModuleData(this.$constants.shareDataKey.REGION_FILTER);
      this.regionFilterData = regionFilterData;
    },

    geometryFilterUpdateHandler() {
      const config = this.configMap[this.activeKey];
      if (!config) return;
      // 获取空间过滤geometry
      const drawFeature = this.getModuleData(this.$constants.shareDataKey.GEOMETRY_FILTER);
      this.geometryFilter = drawFeature ? drawFeature.geometry : null;
    },

    rowSelectHandler(payload) {
      this.bus.$emit(this.$events.table.ROW_SELECT, payload);
    },

    rowUnselectHandler(payload) {
      this.bus.$emit(this.$events.table.ROW_UNSELECT, payload);
    },

    // 加载模块
    async _loadModule(uri) {
      const module = await import(`@/${uri}`);
      return this.$utils.object.deepCloneObj(module.default);
    },

    closeHandler() {
      const keys = Object.keys(this.configMap);
      keys.forEach((k) => {
        this.bus.$emit(this.$events.table.TABLE_CLOSED, k);
      });
      this.configMap = {};
      this.tables = [];
      this.activeKey = null;
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.table-widget {
  /deep/ .ant-tabs .ant-tabs-top-content {
    height: calc(100% - 82px);
  }

  /deep/ .ant-tabs-tabpane.ant-tabs-tabpane-active {
    height: 100%;
  }

  /deep/ .ant-tabs-card {
    height: 100%;
  }

  /deep/ .ant-tabs-bar {
    margin: 0 0 8px 0;
  }

  .btn-close {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
    color: #00000073;
    z-index: 1;

    &:hover {
      color: #000;
    }
  }
}
</style>
