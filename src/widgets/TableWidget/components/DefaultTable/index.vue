<template>
  <div class="default-table-wrapper">
    <div class="search-box">
      <!-- 搜索栏 -->
      <div v-for="(item, index) in searchFields" :key="index">
        <a-input-search v-if="item.type === 'text'" v-model="item.value" style="width: 200px" />
      </div>
      <a-button type="primary" ghost @click="resetSearch">重置</a-button>
      <a-button type="primary" @click="onSearch">搜索</a-button>
    </div>
    <a-table
      ref="table"
      bordered
      :columns="columns"
      size="middle"
      :data-source="dataSource"
      :scroll="{ y: '100%', x: minWidth }"
      :pagination="false"
      :rowKey="primaryKey"
      :rowClassName="rowClassNameHandler"
      :customRow="customRowHandler"
      @change="rowChangeHandler"
    >
    </a-table>
    <a-pagination
      v-model="pageCurrent"
      :pageSize="pageSize"
      :total="pageTotal"
      :pageSizeOptions="pageSizeOptions"
      :showSizeChanger="true"
      @change="pageChangeHandler"
      @showSizeChange="showSizeChangeHandler"
    >
      <template slot="buildOptionText" slot-scope="props">
        <span>{{ props.value }}条/页</span>
      </template>
    </a-pagination>
  </div>
</template>

<script>
import axios from "axios";

const INDEX_NAME = "__index__";
const INDEX_LABEL = "序号";
const INDEX_WIDTH = 60;
const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_PAGE_CURRENT = 1;
const DEFAULT_PAGE_TOTAL = 100;
const DEFAULT_PRIMARY_KEY = "_id";

export default {
  name: "DefaultTable",

  props: {
    config: {
      type: Object,
    },
    geometryFilter: {
      type: Object,
    },
    regionFilterData: {
      type: Object,
    },
  },

  data() {
    return {
      columns: null,
      dataSource: null,
      primaryKey: DEFAULT_PRIMARY_KEY,
      filter: null,
      regionFilter: null,
      minWidth: 0,
      pageCurrent: DEFAULT_PAGE_CURRENT,
      pageSize: DEFAULT_PAGE_SIZE,
      pageTotal: DEFAULT_PAGE_TOTAL,
      pageSizeOptions: ["10", "20", "30", "40", "50"],
      searchFields: [],
      searchFilter: [],
    };
  },

  mounted() {
    this.show();
    console.log("config------>", this.config);
  },

  watch: {
    geometryFilter() {
      this.refreshTableData();
    },

    regionFilterData() {
      this.refreshTableData();
    },
  },

  methods: {
    // 重置搜索
    resetSearch() {
      this.searchFields = this.searchFields.map((item) => {
        switch (item.type) {
          case "text":
            item.value = "";
            break;
          default:
            item.value = "";
            break;
        }
        return item;
      });
      this.searchFilter = [];
    },
    // 搜索
    onSearch() {
      console.log(this.searchFields, "searchFields");

      this.searchFilter = [];
      this.searchFields.forEach((item) => {
        switch (item.type) {
          case "text":
            if (item.value) this.searchFilter.push(["like", item.field, item.value]);
            break;
          default:
            break;
        }
      });
      this.refreshTableData();
    },
    // 显示表格
    async show() {
      if (!this.config || !this.config.key) return;
      // 获取图层的配置信息
      const data = await this.getTableConfig(this.config.config);
      // 生成表头
      let columns = [];
      console.log(data, "columns");
      if (data.table && data.table.columns) {
        columns = data.table.columns.map((c) => {
          let obj = {
            title: c.label,
            key: c.field,
            width: c.width,
            dataIndex: c.field,
            // 排序
            //sorter: c.sortable == true ? (a, b) => a[c.field] - b[c.field]: false,  // 本地排序
            sorter: c.sortable == true,
            // 小数位数
            customRender: c.decimals > 0 ? (text) => parseFloat(text).toFixed(c.decimals) : false,
          };
          if (c.dictRef && data.table.dicts && data.table.dicts[c.dictRef]) {
            obj.customRender = (e) => {
              return data.table.dicts[c.dictRef][e];
            };
          }
          return obj;
        });
      }
      // 主键
      this.primaryKey = !data.primaryKey ? this.primaryKey : data.primaryKey;
      // 过滤条件
      this.filter = data.table?.filter;
      // 搜索
      this.searchFields = data.searchFields ? data.searchFields : [];
      // region filter
      this.regionFilter = data.regionFilter;
      // 最小宽度
      this.minWidth = data.table?.minWidth;
      // 是否显示序号
      if (data.table.showIndex === true) {
        columns = [
          {
            title: INDEX_LABEL,
            key: INDEX_NAME,
            width: INDEX_WIDTH,
            dataIndex: INDEX_NAME,
          },
          ...columns,
        ];
      }
      //
      this.columns = columns;

      Object.assign(this.config, {
        // 数据UID
        dataName: data.name,
        // 数据类型
        dataType: data.type,
        orderField: data.orderField,
        orderType: data.orderType,
      });
      // 处理搜索
      this.handleSearchConfig();
      // 加载数据
      this.refreshTableData();
    },

    handleSearchConfig() {
      if (this.searchFields.length === 0 && this.primaryKey === "_id") return;
      else if (this.searchFields.length === 0) {
        this.searchFields.push({
          field: this.primaryKey,
          type: "text",
          value: "",
        });
      }
    },

    async getTableConfig(configUrl) {
      const resp = await axios.get(configUrl);
      console.log("请求结果", resp.data);

      return resp.data;
    },

    // 刷新表格数据
    async refreshTableData() {
      // 返回字段
      const outFields = this.columns.filter((c) => c.key !== INDEX_NAME).map((c) => c.key);
      // 返回字段中添加ID
      outFields.push(this.primaryKey);
      const theFilter = this.getFilter();
      const params = {
        returnGeometry: false,
        format: "json",
        outFields,
        limit: this.pageSize,
        page: this.pageCurrent,
        filter: theFilter,
      };
      // 空间过滤
      if (this.geometryFilter) {
        params.geometry = this.geometryFilter;
      }
      if (this.config.orderField && this.config.orderType) {
        Object.assign(params, {
          orderField: this.config.orderField,
          orderType: this.config.orderType,
        });
      }
      // 获取记录数量
      //const countParams = this.geometryFilter ? { geometry: this.geometryFilter } : null
      const countParams = theFilter ? { filter: theFilter } : null;
      const count = await this.$apis.feature.count(this.config.dataName, this.primaryKey, countParams);
      // 获取记录
      const data = await this.$apis.feature.query(this.config.dataName, params);
      data.forEach((item, index) => (item[INDEX_NAME] = this.pageSize * (this.pageCurrent - 1) + index + 1));
      this.dataSource = data;
      this.pageTotal = count;
    },

    getFilter() {
      let theFilter = ["all"];
      // region过滤
      if (this.regionFilterData && this.regionFilter && this.regionFilter[this.regionFilterData.region]) {
        theFilter.push(["=", this.regionFilter[this.regionFilterData.region], this.regionFilterData.code]);
      }
      if (this.filter) {
        theFilter.push(this.filter);
      }

      if (this.searchFilter.length > 0) {
        theFilter.push(...this.searchFilter);
      }

      if (theFilter.length == 1) {
        return;
      }
      if (theFilter.length == 2) {
        return theFilter[1];
      }
      return theFilter;
    },

    // 翻页重新请求数据
    pageChangeHandler() {
      this.refreshTableData();
    },

    // 每页显示数量变化重新请求数据
    showSizeChangeHandler(current, pageSize) {
      this.pageSize = pageSize;
      this.refreshTableData();
    },

    // 排序重新请求数据
    rowChangeHandler(pagination, filters, sorter) {
      let { field, order } = sorter;
      if (order) {
        order = order === "ascend" ? "ASC" : "DESC";
      } else {
        field = order = null;
      }
      this.config.orderField = field;
      this.config.orderType = order;
      this.refreshTableData();
    },

    //
    rowClassNameHandler(record) {
      return record[this.primaryKey] == this.config.selectedRecordId ? "selected" : "";
    },

    // 行点击选中
    customRowHandler(record) {
      return {
        on: {
          click: () => {
            const select = !(record[this.primaryKey] === this.config.selectedRecordId);
            const selectedRecordId = select ? record[this.primaryKey] : null;
            // 高亮选中或者取消高亮选中
            this.$set(this.config, "selectedRecordId", selectedRecordId);
            // 派发选中事件
            const event = select ? "rowSelect" : "rowUnselect";
            this.$emit(event, {
              key: this.config.key,
              recordId: record[this.primaryKey],
            });
          },
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
@import "@/assets/styles/common.less";

.default-table-wrapper {
  height: calc(100% - 68px);
}

/deep/ .ant-table-wrapper,
/deep/ .ant-spin-nested-loading,
/deep/ .ant-spin-container,
/deep/ .ant-table,
/deep/ .ant-table-content {
  height: 100%;
}

/deep/ .ant-table-scroll {
  height: 100%;
}

/deep/ .ant-table-body {
  height: calc(100% - 68px);
  overflow-y: auto;
}

/deep/ .ant-table-empty .ant-table-body {
  height: 0;
}

/deep/ .ant-table-pagination.ant-pagination {
  margin: 8px 0 16px 0;
}

.ant-pagination {
  margin-top: 10px;
  text-align: center;
}

/deep/ .ant-table-row.selected:not(.ant-table-expanded-row) > td {
  background: rgba(24, 144, 255, 0.4);
}

/deep/ tr:hover:not(.ant-table-expanded-row) > td {
  background: rgba(24, 144, 255, 0.2);
}

/deep/ .ant-table-content table tr > th,
/deep/ .ant-table-content table tr > td {
  padding: 8px;
}
.search-box {
  margin: 10px 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}
</style>
