# ListTable 组件滚动到底部监听功能

## 功能概述

ListTable 组件现在支持滚动到底部的监听功能，当用户滚动到表格底部时会触发回调事件。这个功能特别适用于实现无限滚动、自动分页加载等场景。

## 主要特性

1. **滚动到底部监听**: 当滚动条接近底部时（允许10px误差）触发回调
2. **支持for循环使用**: 通过唯一的instanceId区分不同的表格实例
3. **防抖优化**: 内置防抖功能，避免滚动事件频繁触发，可自定义防抖延迟时间
4. **自动清理**: 组件销毁时自动移除事件监听器，避免内存泄漏
5. **灵活的回调**: 回调函数提供instanceId和scrollElement信息

## 使用方法

### 基本用法

```vue
<template>
  <ListTable
    :layout="tableConfig.layout"
    :res="tableData"
    instanceId="my-table"
    :debounceDelay="300"
    @scrollToBottom="handleScrollToBottom"
  />
</template>

<script>
export default {
  methods: {
    handleScrollToBottom(data) {
      console.log('滚动到底部了！', data.instanceId);
      // 在这里处理滚动到底部的逻辑，比如加载更多数据
      this.loadMoreData();
    },
    
    loadMoreData() {
      // 加载更多数据的逻辑
    }
  }
}
</script>
```

### 在for循环中使用

```vue
<template>
  <div v-for="table in tables" :key="table.id">
    <ListTable
      :layout="table.layout"
      :res="table.data"
      :instanceId="table.id"
      :debounceDelay="500"
      @scrollToBottom="(data) => handleScrollToBottom(data, table.id)"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      tables: [
        { id: 'table-1', layout: {...}, data: [...] },
        { id: 'table-2', layout: {...}, data: [...] },
        { id: 'table-3', layout: {...}, data: [...] }
      ]
    }
  },
  methods: {
    handleScrollToBottom(data, tableId) {
      console.log(`表格 ${tableId} 滚动到底部了！`);
      
      // 根据不同的表格ID执行不同的逻辑
      switch(tableId) {
        case 'table-1':
          this.loadMoreDataForTable1();
          break;
        case 'table-2':
          this.loadMoreDataForTable2();
          break;
        case 'table-3':
          this.loadMoreDataForTable3();
          break;
      }
    }
  }
}
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| res | Array | - | 表格数据 |
| layout | Object | - | 表格布局配置 |
| instanceId | String/Number | 随机生成 | 表格实例的唯一标识符，用于在for循环中区分不同实例 |
| debounceDelay | Number | 300 | 滚动事件防抖延迟时间（毫秒） |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| scrollToBottom | { instanceId, scrollElement } | 滚动到底部时触发 |
| rowItemClick | item | 行点击时触发 |

## 回调参数说明

### scrollToBottom 事件参数

```javascript
{
  instanceId: 'table-1',        // 表格实例ID
  scrollElement: HTMLElement    // 滚动容器的DOM元素
}
```

## 实际应用示例

### 自动分页加载

在 TabContent 组件中，已经集成了滚动到底部自动加载下一页的功能：

```javascript
handleScrollToBottom(data) {
  console.log(`表格 ${data.instanceId} 滚动到底部了！`);
  
  // 如果还有下一页且不在加载中，自动加载下一页
  if (this.hasNext && this.loading) {
    this.nextPage();
  }
}
```

### 无限滚动

```javascript
handleScrollToBottom(data) {
  // 检查是否还有更多数据
  if (this.hasMoreData && !this.isLoading) {
    this.isLoading = true;
    
    // 调用API加载更多数据
    this.loadMoreData().then(newData => {
      this.tableData.push(...newData);
      this.isLoading = false;
    });
  }
}
```

## 防抖功能说明

组件内置了防抖功能来优化滚动事件的性能：

- **默认延迟**: 300毫秒
- **可配置**: 通过 `debounceDelay` prop 自定义延迟时间
- **自动清理**: 组件销毁时会自动清理防抖定时器

### 防抖延迟时间建议

- **快速响应场景**: 100-200ms（如实时搜索）
- **一般场景**: 300ms（默认值，适合大多数情况）
- **性能优先场景**: 500-1000ms（减少频繁触发）

```vue
<!-- 快速响应 -->
<ListTable :debounceDelay="200" @scrollToBottom="handleScrollToBottom" />

<!-- 性能优先 -->
<ListTable :debounceDelay="800" @scrollToBottom="handleScrollToBottom" />
```

## 注意事项

1. **唯一性**: 在for循环中使用时，确保每个表格实例都有唯一的instanceId
2. **防抖优化**: 组件已内置防抖功能，无需在回调中再次处理
3. **内存管理**: 组件会自动清理事件监听器和防抖定时器，无需手动处理
4. **误差范围**: 滚动到底部的判断允许10px的误差，避免因为像素精度问题导致无法触发

## 技术实现

- 使用 `ref` 替代固定ID，避免在for循环中产生重复ID
- 在 `mounted` 钩子中添加滚动事件监听
- 在 `beforeDestroy` 钩子中清理事件监听器和防抖定时器
- 使用 `setTimeout` 实现防抖功能，避免频繁触发回调
- 通过 `$emit` 向父组件发送滚动到底部事件

### 防抖实现原理

```javascript
this.scrollHandler = () => {
  // 清除之前的防抖定时器
  this.clearDebounceTimer();

  // 设置新的防抖定时器
  this.debounceTimer = setTimeout(() => {
    // 检查是否滚动到底部
    if (scrollElement.scrollHeight - scrollElement.scrollTop <= scrollElement.clientHeight + 10) {
      this.$emit("scrollToBottom", { instanceId, scrollElement });
    }
  }, this.debounceDelay);
};
```
