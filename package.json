{"name": "gt-onemap-framework", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-dev": "node scripts/build-dev.js", "lint": "vue-cli-service lint"}, "dependencies": {"@turf/turf": "^6.5.0", "ant-design-vue": "1.7.8", "axios": "^0.21.1", "core-js": "^3.6.5", "echarts": "^5.2.0", "element-resize-detector": "^1.2.3", "gt-mapbox-gl-compare": "^0.5.4", "mapbox-gl": "^1.13.1", "svg-sprite-loader": "^6.0.11", "uid": "2.0.0", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11", "less": "4.1.1", "less-loader": "7.2.0", "babel-plugin-import": "^1.13.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}