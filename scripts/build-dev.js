#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

/**
 * 开发环境构建脚本
 * 自动化配置本地开发环境，包括环境变量和API地址替换
 */

class BuildDevScript {
  constructor() {
    this.projectRoot = path.resolve(__dirname, "..");
    this.backupDir = path.join(this.projectRoot, "backups");
    this.stats = {
      filesProcessed: 0,
      ipReplacements: 0,
      errors: [],
    };

    // 文件配置
    this.files = {
      envProduction: {
        path: path.join(this.projectRoot, ".env.production"),
        content: `NODE_ENV=production
VUE_APP_URI=/gx-source-protection/`,
      },
      config: {
        path: path.join(this.projectRoot, "public/config.json"),
        ipPattern: /39\.104\.87\.15/g,
        replacement: "localhost",
      },
      baseMap: {
        path: path.join(this.projectRoot, "public/config/style/baseMap.json"),
        ipPattern: /39\.104\.87\.15/g,
        replacement: "localhost",
      },
    };
  }

  /**
   * 验证JSON格式
   */
  validateJson(content, filePath) {
    try {
      JSON.parse(content);
      return true;
    } catch (error) {
      throw new Error(`JSON格式验证失败 (${filePath}): ${error.message}`);
    }
  }

  /**
   * 检查文件权限
   */
  checkFilePermissions(filePath) {
    try {
      fs.accessSync(filePath, fs.constants.R_OK | fs.constants.W_OK);
      return true;
    } catch (error) {
      throw new Error(`文件权限检查失败 (${filePath}): 没有读写权限`);
    }
  }

  /**
   * 替换 .env.production 文件内容
   */
  async replaceEnvProduction() {
    console.log("\n🔧 开始处理 .env.production 文件...");

    const { path: filePath, content } = this.files.envProduction;

    try {

      // 写入新内容
      fs.writeFileSync(filePath, content, "utf8");
      console.log(`✅ 成功更新 .env.production 文件`);
      console.log(`   📝 新配置: NODE_ENV=production, VUE_APP_URI=/guangxi-onemap/`);

      this.stats.filesProcessed++;
    } catch (error) {
      const errorMsg = `处理 .env.production 失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 替换JSON文件中的IP地址
   */
  async replaceIpInJsonFile(fileConfig, description) {
    console.log(`\n🔧 开始处理 ${description}...`);

    const { path: filePath, ipPattern, replacement } = fileConfig;

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      // 检查文件权限
      this.checkFilePermissions(filePath);

      // 读取文件内容
      const originalContent = fs.readFileSync(filePath, "utf8");

      // 验证原始JSON格式
      this.validateJson(originalContent, filePath);

      // 执行IP地址替换
      const newContent = originalContent.replace(ipPattern, replacement);

      // 验证替换后的JSON格式
      this.validateJson(newContent, filePath);

      // 统计替换次数
      const matches = originalContent.match(ipPattern);
      const replacementCount = matches ? matches.length : 0;

      if (replacementCount === 0) {
        console.log(`   ℹ️  未找到需要替换的IP地址 (************)`);
      } else {
        // 写入新内容
        fs.writeFileSync(filePath, newContent, "utf8");
        console.log(`   ✅ 成功替换 ${replacementCount} 个IP地址: ************ → ${replacement}`);
        this.stats.ipReplacements += replacementCount;
      }

      this.stats.filesProcessed++;
    } catch (error) {
      const errorMsg = `处理 ${description} 失败: ${error.message}`;
      this.stats.errors.push(errorMsg);
      throw new Error(errorMsg);
    }
  }

  /**
   * 显示操作摘要
   */
  showSummary() {
    console.log("\n" + "=".repeat(60));
    console.log("📊 操作摘要");
    console.log("=".repeat(60));
    console.log(`✅ 处理文件数量: ${this.stats.filesProcessed}`);
    console.log(`🔄 IP地址替换次数: ${this.stats.ipReplacements}`);

    if (this.stats.errors.length > 0) {
      console.log(`❌ 错误数量: ${this.stats.errors.length}`);
      this.stats.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log("🎉 所有操作均成功完成！");
    }

    console.log("\n💡 提示:");
    console.log("   - 现在可以运行 yarn serve 启动开发服务器");
    console.log("=".repeat(60));
  }

  /**
   * 主执行函数
   */
  async run() {
    console.log("🚀 广西农业生态资源环境保护管理系统 - 开发环境构建");
    console.log("📅 执行时间:", new Date().toLocaleString("zh-CN"));
    console.log("📂 项目根目录:", this.projectRoot);

    try {
      // 1. 替换 .env.production 文件
      await this.replaceEnvProduction();

      // 2. 替换 public/config.json 中的IP地址
      await this.replaceIpInJsonFile(this.files.config, "public/config.json");

      // 3. 替换 public/config/style/baseMap.json 中的IP地址
      await this.replaceIpInJsonFile(this.files.baseMap, "public/config/style/baseMap.json");

      // 显示操作摘要
      this.showSummary();

      process.exit(0);
    } catch (error) {
      console.error(`\n❌ 脚本执行失败: ${error.message}`);
      this.showSummary();
      process.exit(1);
    }
  }
}

// 执行脚本
if (require.main === module) {
  const buildDev = new BuildDevScript();
  buildDev.run().catch((error) => {
    console.error("脚本执行出现未捕获的错误:", error);
    process.exit(1);
  });
}

module.exports = BuildDevScript;
